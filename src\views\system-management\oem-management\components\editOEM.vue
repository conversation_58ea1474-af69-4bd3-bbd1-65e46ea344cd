<template>
    <div class="all-paddding-16">
        <div>
            <div class="border"></div>
            <div class="flex top-bottom-center gap-8 t-margin-16">
                <span class="font-24 flex top-bottom-center" style="color: #D66353;">*</span>
                <span class="color-two-grey font-16">oem_key</span>
                <el-input
                    v-model="oem_key"
                    placeholder="请输入渠道名"
                    style="width: 20%;"
                ></el-input>
            </div>
        </div>
        <el-tabs v-model="activeName" class="demo-tabs t-margin-16" >
            <el-tab-pane label="臻企云·数字化产业融合协同平台" name="xtpt">
                <div class="flex top-bottom-center gap-8 b-margin-16">
                    <div style="width: 4px;height: 14px; background-color: #1966FF;" ></div>
                    <span class="color-black font-16">登录页</span>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>域名</span>
                        <el-input
                            v-model="domain"
                            placeholder="请输入域名"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span class="color-two-grey font-16">
                            底部时间
                            <span class="color-three-grey font-14">(默认值为2016-2025)</span>
                        </span>
                        <el-date-picker
                            v-model="cDateValue"
                            type="yearrange"
                            range-separator="To"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            style="width: 100%;"
                        />
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>
                            底部公司名
                            <span class="color-three-grey font-14">(默认值为数组科技（南京）股份有限公司)</span>
                        </span>
                        <el-input
                            v-model="cName"
                            placeholder="请输入公司名"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>
                            左侧顶部文字
                            <span class="color-three-grey font-14">(默认值为臻企云·数字化产业融合协同平台)</span>
                        </span>
                        <el-input
                            v-model="companyName"
                            placeholder="请输入顶部文字"
                        ></el-input>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>左上角logo</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleLogoProgress"
                            :on-success="handleLogoSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="loginLogoUrl && !loginLogoLoading" :src="loginLogoUrl" class="avatar" />
                            <el-icon v-else-if="loginLogoLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>登录页图片</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleLoginProgress"
                            :on-success="handleLoginSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="logoImgUrl && !logoImgLoading" :src="logoImgUrl" class="avatar" />
                            <el-icon v-else-if="logoImgLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>
                            网页页签标题
                            <span class="color-three-grey font-14">(默认值为臻企云·数字化产业融合协同平台)</span>
                        </span>
                        <el-input
                            v-model="webPageTabTitle"
                            placeholder="请输入网页页签标题"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>底部的备案号</span>
                        <el-input
                            v-model="TCP"
                            placeholder="请输入备案号"
                        ></el-input>
                    </div>
                </div>
                <div class="flex top-bottom-center gap-8 tb-margin-16">
                    <div style="width: 4px;height: 14px; background-color: #1966FF;" ></div>
                    <span class="color-black font-16">首页</span>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>首页上方的logo</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleHomeLogoProgress"
                            :on-success="handleHomeLogoSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="logoBkUrl && !logoBkLoading" :src="logoBkUrl" class="avatar" />
                            <el-icon v-else-if="logoBkLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>帮助手册地址</span>
                        <el-input
                            v-model="helpUrl"
                            placeholder="请输入帮助手册地址"
                        ></el-input>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="经营慧眼" name="jyhy">
                <div class="flex space-between top-bottom-center">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>首页logo</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleMpLogoProgress"
                            :on-success="handleMpLogoSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="mpLogoUrl && !mpLogoLoading" :src="mpLogoUrl" class="avatar" />
                            <el-icon v-else-if="mpLogoLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>首页顶部图</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleMpHomeBannerProgress"
                            :on-success="handleMpHomeBannerSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="mpHomeBannerUrl && !mpHomeBannerLoading" :src="mpHomeBannerUrl" class="avatar" />
                            <el-icon v-else-if="mpHomeBannerLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>首页搜索框下方图</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleMpHomeSpecProgress"
                            :on-success="handleMpHomeSpecSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="mpHomeSpecUrl && !mpHomeSpecLoading" :src="mpHomeSpecUrl" class="avatar" />
                            <el-icon v-else-if="mpHomeSpecLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span class="color-two-grey font-16">是否开始精简授权</span>
                        <el-switch v-model="easyCollect" />
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>是否开启票易融</span>
                        <el-switch v-model="PYR" />
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span class="color-two-grey font-16">
                            业务进件二维码海报图
                        </span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleJinrongEduApplyBackimgProgress"
                            :on-success="handleJinrongEduApplyBackimgSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="mpQrCode && !mpQrCodeLoading" :src="mpQrCode" class="avatar" />
                            <el-icon v-else-if="mpQrCodeLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>业务进件分享图</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleJinrongEduShareBackimgProgress"
                            :on-success="handleJinrongEduShareBackimgSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="qrCodeImg && !qrCodeImgLoading" :src="qrCodeImg" class="avatar" />
                            <el-icon v-else-if="qrCodeImgLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span class="color-two-grey font-16">
                            业务进件二维码信息
                            <span class="color-three-grey font-14">(大小，距离顶部，距离左边(50,0,0))</span>
                        </span>
                        <el-input
                            v-model="mpQrcodeText"
                            placeholder="请输大小，距离顶部，距离左边(50,0,0)"
                        ></el-input>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="报告" name="bg">
                <div class="flex space-between top-bottom-center">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>高企报告封面</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleGqCoverProgress"
                            :on-success="handleGqCoverSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="gqCoverUrl && !gqCoverLoading" :src="gqCoverUrl" class="avatar" />
                            <el-icon v-else-if="gqCoverLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>税务报告封面</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleSwCoverProgress"
                            :on-success="handleSwCoverSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="swCoverUrl && !swCoverLoading" :src="swCoverUrl" class="avatar" />
                            <el-icon v-else-if="swCoverLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>发票报告封面</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleFpCoverProgress"
                            :on-success="handleFpCoverSuccess"
                            :before-upload="beforeAvatarUpload"
                        >
                            <img v-if="fpCoverUrl && !fpCoverLoading" :src="fpCoverUrl" class="avatar" />
                            <el-icon v-else-if="fpCoverLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="其他" name="other">
                <div class="flex space-between top-bottom-center">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>数据大屏标题</span>
                        <el-input
                            v-model="dashboardTitle"
                            placeholder="请输入数据大屏标题"
                        ></el-input>
                    </div>
                </div>
                <div class="flex top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 100%;">
                        <span>授权协议</span>
                        <div ref="quillEditor" style="height: 300px;"></div>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="save()" :loading="loading" >
                确定
            </el-button>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import type { UploadProps } from 'element-plus'
import type { OEMUploadItem, XTPT, JYHY, bgCover, other } from '@/types/OEM'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'
import systemService from '@/service/systemService'
import { ElMessage } from 'element-plus'
import type { IOEMListResponseItem } from '@/types/OEM'

const props = defineProps<{
    addData:IOEMListResponseItem,
    refresh: () => void;
}>()

// Quill富文本编辑器相关
const quillEditor = ref<HTMLElement | null>(null)
const quillInstance = ref<Quill | null>(null)

// 初始化Quill编辑器
const initQuillEditor = () => {
    if (quillEditor.value) {
        quillInstance.value = new Quill(quillEditor.value, {
            theme: 'snow',
            placeholder: '请输入授权协议内容...',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'align': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['link'],
                    ['clean']
                ]
            }
        })

        // 监听内容变化，避免循环更新
        quillInstance.value.on('text-change', () => {
            if (quillInstance.value) {
                const newContent = quillInstance.value.root.innerHTML
                if (newContent !== authAgreement.value) {
                    authAgreement.value = newContent
                }
            }
        })

        // 设置初始内容
        if (authAgreement.value) {
            quillInstance.value.root.innerHTML = authAgreement.value
        }
    }
}

const loading = ref(false)
const activeName = ref('xtpt')

// 上传文件接口
const uploadFile = `/api/zhenqi-crm/file/upload-temp`
// 请求头
const token = localStorage.getItem('access_token')
const headers = {
    'Shu-Auth': token ? `Bearer ${JSON.parse(token)}` : ''
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
    if (rawFile.size / 1024 / 1024 > 5) {
        ElMessage.error('文件大小不能超过5mb!')
        return false
    }
    return true
}

// ======协同平台======
// cDate转化
const cDateValue = ref([] as Date[])
const cDate = computed(() => {
    // console.log('JSON.stringify(cDateValue.value)',JSON.stringify(cDateValue.value))
    if(JSON.stringify(cDateValue.value).includes('null')){
        return ''
    }else{
        if(cDateValue.value && cDateValue.value.length === 2) {
            return transformDate(cDateValue.value)
        }else{
            return ''
        }
    }

})
const transformDate = (data : Date[]) => {
    const startYear = data[0].getFullYear()
    const endYear = data[1].getFullYear()
    return `${startYear}-${endYear}`
}

const oem_key = ref('')
const domain = ref('')
const cName = ref('')
const companyName = ref('')
const webPageTabTitle = ref('')
const TCP = ref('')
const helpUrl = ref('')

// 左上角logo上传相关
const loginLogoUrl = ref('')
const loginLogoLoading = ref(false)
const handleLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    loginLogoLoading.value = true
}
const handleLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    loginLogoLoading.value = false
    loginLogoUrl.value = response.data.link
}

// 登录页图片上传相关
const logoImgUrl = ref('')
const logoImgLoading = ref(false)
const handleLoginProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    logoImgLoading.value = true
}
const handleLoginSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, uploadFile)
    logoImgLoading.value = false
    logoImgUrl.value = response.data.link
}

// 首页上方的logo上传相关
const logoBkUrl = ref('')
const logoBkLoading = ref(false)
const handleHomeLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    logoBkLoading.value = true
}
const handleHomeLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response)
    logoBkLoading.value = false
    logoBkUrl.value = response.data.link
}

// ======经营慧眼======
const easyCollect = ref(false)
const PYR = ref(false)
const mpQrcodeText = ref('')

// 首页logo上传相关
const mpLogoUrl = ref('')
const mpLogoLoading = ref(false)
const handleMpLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpLogoLoading.value = true
}
const handleMpLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpLogoLoading.value = false
    mpLogoUrl.value = response.data.link
}

// 首页顶部图上传相关
const mpHomeBannerUrl = ref('')
const mpHomeBannerLoading = ref(false)
const handleMpHomeBannerProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpHomeBannerLoading.value = true
}
const handleMpHomeBannerSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpHomeBannerLoading.value = false
    mpHomeBannerUrl.value = response.data.link
}

// 首页搜索框下方图上传相关
const mpHomeSpecUrl = ref('')
const mpHomeSpecLoading = ref(false)
const handleMpHomeSpecProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpHomeSpecLoading.value = true
}
const handleMpHomeSpecSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpHomeSpecLoading.value = false
    mpHomeSpecUrl.value = response.data.link
}

// 业务进件二维码海报图上传相关
const mpQrCode = ref('')
const mpQrCodeLoading = ref(false)
const handleJinrongEduApplyBackimgProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpQrCodeLoading.value = true
}
const handleJinrongEduApplyBackimgSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpQrCodeLoading.value = false
    mpQrCode.value = response.data.link
}

// 业务进件分享图上传相关
const qrCodeImg = ref('')
const qrCodeImgLoading = ref(false)
const handleJinrongEduShareBackimgProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    qrCodeImgLoading.value = true
}
const handleJinrongEduShareBackimgSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    qrCodeImgLoading.value = false
    qrCodeImg.value = response.data.link
}

// ======报告======
// 高企报告封面上传相关
const gqCoverUrl = ref('')
const gqCoverLoading = ref(false)
const handleGqCoverProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    gqCoverLoading.value = true
}
const handleGqCoverSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    gqCoverLoading.value = false
    gqCoverUrl.value = response.data.link
}

// 税务报告封面上传相关
const swCoverUrl = ref('')
const swCoverLoading = ref(false)
const handleSwCoverProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    swCoverLoading.value = true
}
const handleSwCoverSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    swCoverLoading.value = false
    swCoverUrl.value = response.data.link
}

// 发票报告封面上传相关
const fpCoverUrl = ref('')
const fpCoverLoading = ref(false)
const handleFpCoverProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    fpCoverLoading.value = true
}
const handleFpCoverSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    fpCoverLoading.value = false
    fpCoverUrl.value = response.data.link
}

// ======其他======
const dashboardTitle = ref('')
const authAgreement = ref('')

// 传参
const updateData = ref<OEMUploadItem>({
    id:'',
    key:'',
    modules:[
        {
            productType:0,
            config:{
               
            }
        },
        {
            productType:1,
            config:{
               
            }
        },
        {
            productType:2,
            config:{
              
            }
        },
        {
            productType:3,
            config:{
               
            }
        },
    ]
})

watch(oem_key, () => updateData.value.key = oem_key.value, { immediate: true, deep: true })

// 协同平台相关
watch([domain, cDate, cName, companyName, webPageTabTitle, TCP, helpUrl, loginLogoUrl, logoImgUrl, logoBkUrl], () => {
    // 确保 config 对象存在
    if (!updateData.value.modules[0].config) {
        updateData.value.modules[0].config = {}
    }
    // 断言
    const xtptConfig = updateData.value.modules[0].config as XTPT
    xtptConfig.domain = domain.value
    xtptConfig.cDate = cDate.value
    xtptConfig.cName = cName.value
    xtptConfig.companyName = companyName.value
    xtptConfig.tcp = TCP.value
    xtptConfig.helpManualAddress = helpUrl.value
    xtptConfig.loginLogo = loginLogoUrl.value
    xtptConfig.logoImg = logoImgUrl.value
    xtptConfig.logoBk = logoBkUrl.value
    xtptConfig.webPageTabTitle = webPageTabTitle.value

}, { immediate: true, deep: true })

// 经营慧眼相关
watch([mpLogoUrl, mpHomeBannerUrl, mpHomeSpecUrl, easyCollect, PYR, mpQrCode, qrCodeImg, mpQrcodeText], () => {
    // 确保 config 对象存在
    if (!updateData.value.modules[1].config) {
        updateData.value.modules[1].config = {}
    }
    // 断言
    const jyhyConfig = updateData.value.modules[1].config as JYHY
    jyhyConfig.mpLogo = mpLogoUrl.value
    jyhyConfig.mpHomeBanner = mpHomeBannerUrl.value
    jyhyConfig.mpHomeSpec = mpHomeSpecUrl.value
    jyhyConfig.easyCollect = easyCollect.value
    jyhyConfig.pyr = PYR.value
    jyhyConfig.mpQrCode = mpQrCode.value
    jyhyConfig.qrCodeImg = qrCodeImg.value
    jyhyConfig.mpQrcodeText = mpQrcodeText.value
})

// 报告相关
watch([gqCoverUrl, swCoverUrl, fpCoverUrl], () => {
    if (!updateData.value.modules[1].config) {
        updateData.value.modules[1].config = {}
    }
    const bgConfig = updateData.value.modules[2].config as bgCover
    bgConfig.gqbgCover = gqCoverUrl.value
    bgConfig.swbgCover = swCoverUrl.value
    bgConfig.fpbgCover = fpCoverUrl.value
})

// 其他
watch([dashboardTitle, authAgreement],() => {
    if (!updateData.value.modules[1].config) {
        updateData.value.modules[1].config = {}
    }
    const otherConfig = updateData.value.modules[3].config as other
    otherConfig.dashboardTitle = dashboardTitle.value
    otherConfig.sqxy = authAgreement.value
})

// 编辑的时候回显
watch(() => props.addData, (val) => {
    if (!val) return // 如果没有数据则不执行
    console.log('*********',val)
    oem_key.value = val.key
    // 协同平台
    domain.value = val.platform?.domain || ''
    cDateValue.value = val.platform?.cDate
        ? val.platform.cDate.split('-').map(dateStr => new Date(dateStr))
        : [new Date(''), new Date('')]
    console.log('cDateValue',cDateValue.value)
    console.log('cDate',cDate.value)
    cName.value = val.platform?.cName || ''
    companyName.value = val.platform?.companyName || ''
    loginLogoUrl.value = val.platform?.loginLogo || ''
    logoImgUrl.value = val.platform?.logoImg || ''
    webPageTabTitle.value = val.platform?.webPageTabTitle || ''
    TCP.value = val.platform?.tcp || ''
    logoBkUrl.value = val.platform?.logoBk || ''
    helpUrl.value = val.platform?.helpManualAddress || ''
    // 经营慧眼
    mpLogoUrl.value = val.cloudService?.mpLogo || ''
    mpHomeBannerUrl.value = val.cloudService?.mpHomeBanner || ''
    mpHomeSpecUrl.value = val.cloudService?.mpHomeSpec || ''
    easyCollect.value = val.cloudService?.easyCollect || false
    PYR.value = val.cloudService?.pyr || false
    mpQrCode.value = val.cloudService?.mpQrCode || ''
    qrCodeImg.value = val.cloudService?.qrCodeImg || ''
    mpQrcodeText.value = val.cloudService?.mpQrcodeText || ''
    // 报告相关
    gqCoverUrl.value = val.report?.gqbgCover || ''
    swCoverUrl.value = val.report?.swbgCover || ''
    fpCoverUrl.value = val.report?.fpbgCover || ''
    // 其他
    dashboardTitle.value = val.other?.dashboardTitle || ''
    authAgreement.value = val.other?.sqxy || ''
    // 更新富文本编辑器内容
    nextTick(() => {
        if (quillInstance.value && authAgreement.value) {
            quillInstance.value.root.innerHTML = authAgreement.value
        }
    })
}, { immediate: true })

// 监听 authAgreement 变化，同步更新富文本编辑器
watch(authAgreement, (newValue) => {
    if (quillInstance.value && newValue !== quillInstance.value.root.innerHTML) {
        quillInstance.value.root.innerHTML = newValue
    }
}, { immediate: true })

// 重置所有数据
const resetAllData = () => {
    oem_key.value = ''
    
    // 重置协同平台
    domain.value = ''
    cName.value = ''
    companyName.value = ''
    webPageTabTitle.value = ''
    TCP.value = ''
    helpUrl.value = ''
    cDateValue.value = []
    loginLogoUrl.value = ''
    logoImgUrl.value = ''
    logoBkUrl.value = ''

    // 重置经营慧眼相关
    easyCollect.value = false
    PYR.value = false
    mpQrcodeText.value = ''
    mpLogoUrl.value = ''
    mpHomeBannerUrl.value = ''
    mpHomeSpecUrl.value = ''
    mpQrCode.value = ''
    qrCodeImg.value = ''

    // 重置报告相关
    gqCoverUrl.value = ''
    swCoverUrl.value = ''
    fpCoverUrl.value = ''

    // 重置其他
    dashboardTitle.value = ''
    authAgreement.value = ''

    // 重置富文本编辑器
    if (quillInstance.value) {
        quillInstance.value.setText('')
    }

    // 重置 updateData
    updateData.value = {
        id:'',
        key:'',
        modules:[
            {
                productType:0,
                config:{

                }
            },
            {
                productType:1,
                config:{

                }
            },
            {
                productType:2,
                config:{

                }
            },
            {
                productType:3,
                config:{

                }
            },
        ]
    }
}

const save = () => {
    loading.value = true
    console.log('123123',updateData.value)
    if(oem_key.value === props.addData.key){
        delete updateData.value.key
    }
    updateData.value.id = props.addData.id
    systemService.systemOEMEdit(updateData.value).then(res => {
        console.log(res)
        if(res.success){
            ElMessage.success('保存成功')
            resetAllData() 
            handleClose()
            props.refresh()
        }else{
            ElMessage.error(res.errMsg)
        }
        loading.value = false
    })
}

const emit = defineEmits(['cancel'])
const handleClose = () => {
    emit('cancel')
}


// 组件挂载后初始化编辑器
onMounted(() => {
    nextTick(() => {
        initQuillEditor()
    })
})

</script>

<style lang='scss' scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}


.dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    margin-top: 20px;
}


:deep(.avatar-uploader .avatar) {
  width: 44px;
  height: 44px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 44px;
  height: 44px;
  text-align: center;
}

// Quill编辑器样式
:deep(.ql-toolbar) {
  border: 1px solid #dcdfe6;

  background-color: #fafafa;
  border-radius: 4px 4px 4px 4px;
}

:deep(.ql-container) {
  border: 1px solid #dcdfe6;
  font-size: 14px;
  border-radius: 0 0 4px 4px;
}

:deep(.ql-editor) {
  min-height: 200px;
  line-height: 1.6;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}

</style>