import type { IAicConditionData, IAicNormalSearchRules, INormalFilterParams } from './aic'
import type { IMenuResponse } from './menu'
import type { IUserAccountInfoResponse } from './user'
import type { IOEMListResponseItem } from './OEM'

// 声明根状态类型
import type { IHighSearchRules } from '@/types/model'

export interface RootState {
    auth: AuthState
    user: UserState
    app: AppState
    enterprise: EnterpriseState
    menu: MenuTreeState
}

export interface AuthState {
    isLogin: boolean
    accessToken: string
    dataScope: Record<string, number>[]
    oemConfig: IOEMListResponseItem | null
}

export interface UserState {
    account: IUserAccountInfoResponse | null
}

export interface AppState {
    hightSearchRulesData: IHighSearchRules[] | null
    staticConfig: IAicConditionData | null
    normalSearchRulesData: IAicNormalSearchRules[]
    tenderSearchRulesData: IAicNormalSearchRules[]
    factorySearchRulesData: IAicNormalSearchRules[]
    projectSearchRulesData: IAicNormalSearchRules[]
    matchRulesData: IAicNormalSearchRules[]
    lastEntSearchRulesData: IAicNormalSearchRules[]
}

export interface EnterpriseState {
    normalFilterParams: INormalFilterParams[]
    removeTarget: INormalFilterParams | null
    tenderFilterParams: INormalFilterParams[]
    projectFilterParams: INormalFilterParams[]
    factoryFilterParams: INormalFilterParams[]
    lastEntFilterParams: INormalFilterParams[]
}

export interface MenuTreeState {
    menuTree: IMenuResponse[]
}
